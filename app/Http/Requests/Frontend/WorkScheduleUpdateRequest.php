<?php

namespace App\Http\Requests\Frontend;

use App\Enums\Day;
use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WorkScheduleUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        $workSchedule = $this->route('workSchedule');

        return [
            'type' => ['required', Rule::enum(WorkScheduleType::class)],
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::uniqueTenant('work_schedules', 'name')->ignore($workSchedule->id),
            ],
            'work_and_off_days_distribution_type' => [
                'required',
                Rule::enum(WorkAndOffDaysDistributionType::class),
            ],
            'off_days_after_each_repetition' => [
                'required_if:work_and_off_days_distribution_type,' .
                WorkAndOffDaysDistributionType::NumberOfDays->value,
                'nullable',
                'integer',
                'min:0',
                'max:30',
            ],
            'start_date' => ['required', 'date'],
            'workdays' => [
                'required',
                'array',
                'min:1',
                function ($attribute, $value, $fail) {
                    $type = $this->input('type');

                    // Fixed schedules must have exactly one workday
                    if ($type === WorkScheduleType::Fixed->value && count($value) !== 1) {
                        $fail('Fixed work schedules must have exactly one workday.');
                        return;
                    }

                    // Rotational schedules must have multiple workdays
                    if ($type === WorkScheduleType::Rotational->value && count($value) < 2) {
                        $fail('Rotational work schedules must have at least two workdays.');
                        return;
                    }
                },
            ],
            'workdays.*.workday_id' => [
                'required',
                'integer',
                Rule::existsTenant('workdays', 'id'),
            ],
            'workdays.*.specific_days' => [
                'required_if:work_and_off_days_distribution_type,' .
                WorkAndOffDaysDistributionType::SpecificDays->value,
                'prohibited_if:work_and_off_days_distribution_type,' .
                WorkAndOffDaysDistributionType::NumberOfDays->value,
                'nullable',
                'array',
                'min:1',
            ],
            'workdays.*.specific_days.*' => ['string', Rule::enum(Day::class)],
            'workdays.*.work_days_number' => [
                'required_if:work_and_off_days_distribution_type,' .
                WorkAndOffDaysDistributionType::NumberOfDays->value,
                'prohibited_if:work_and_off_days_distribution_type,' .
                WorkAndOffDaysDistributionType::SpecificDays->value,
                'nullable',
                'integer',
                'min:1',
                'max:30',
            ],
            'workdays.*.off_days_number' => [
                'required_if:work_and_off_days_distribution_type,' .
                WorkAndOffDaysDistributionType::NumberOfDays->value,
                'prohibited_if:work_and_off_days_distribution_type,' .
                WorkAndOffDaysDistributionType::SpecificDays->value,
                'nullable',
                'integer',
                'min:0',
                'max:30',
            ],
            'workdays.*.repetitions_number' => [
                function ($attribute, $value, $fail) {
                    $type = $this->input('type');
                    $distributionType = $this->input('work_and_off_days_distribution_type');

                    // Required for rotational schedules with number of days distribution
                    if ($type === WorkScheduleType::Rotational->value &&
                        $distributionType === WorkAndOffDaysDistributionType::NumberOfDays->value &&
                        is_null($value)) {
                        $fail('Repetitions number is required for rotational schedules with number of days distribution.');
                        return;
                    }

                    // Prohibited for fixed schedules
                    if ($type === WorkScheduleType::Fixed->value && !is_null($value)) {
                        $fail('Repetitions number cannot be used with fixed schedules.');
                        return;
                    }

                    // Prohibited for specific days distribution
                    if ($distributionType === WorkAndOffDaysDistributionType::SpecificDays->value && !is_null($value)) {
                        $fail('Repetitions number cannot be used with specific days distribution.');
                        return;
                    }
                },
                'nullable',
                'integer',
                'min:1',
                'max:52',
            ],
            'assignments' => ['nullable', 'array'],
            'assignments.employees' => ['nullable', 'array'],
            'assignments.employees.*' => ['integer', Rule::existsTenant('employees', 'id')],
            'assignments.departments' => ['nullable', 'array'],
            'assignments.departments.*' => ['integer', Rule::existsTenant('departments', 'id')],
            'assignments.direct_managers' => ['nullable', 'array'],
            'assignments.direct_managers.*' => ['integer', Rule::existsTenant('employees', 'id')],
            'assignments.tags' => ['nullable', 'array'],
            'assignments.tags.*' => ['integer', Rule::existsTenant('tags', 'id')],
            'assignments.locations' => ['nullable', 'array'],
            'assignments.locations.*' => ['integer', Rule::existsTenant('locations', 'id')],
        ];
    }

    public function messages(): array
    {
        return [
            'workdays.required' => 'At least one workday must be selected.',
            'workdays.min' => 'At least one workday must be selected.',
            'workdays.*.specific_days.required_if' =>
                'Specific days must be selected when using specific days distribution.',
            'workdays.*.specific_days.prohibited_if' =>
                'Specific days cannot be used with number of days distribution.',
            'workdays.*.specific_days.min' => 'At least one day must be selected.',
            'off_days_after_each_repetition.required_if' =>
                'Off days after each repetition is required when using number of days distribution.',
            'workdays.*.work_days_number.required_if' =>
                'Work days number is required when using number of days distribution.',
            'workdays.*.work_days_number.prohibited_if' =>
                'Work days number cannot be used with specific days distribution.',
            'workdays.*.off_days_number.required_if' =>
                'Off days number is required when using number of days distribution.',
            'workdays.*.off_days_number.prohibited_if' =>
                'Off days number cannot be used with specific days distribution.',
            'workdays.*.repetitions_number.required_if' =>
                'Repetitions number is required for rotational schedules with number of days distribution.',
            'workdays.*.repetitions_number.prohibited_if' =>
                'Repetitions number cannot be used with fixed schedules or specific days distribution.',
        ];
    }
}
