<?php

namespace App\Jobs;

use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkdayType;
use App\Enums\WorkScheduleType;
use App\Models\Employee;
use App\Models\Holiday;
use App\Models\WorkSchedule;
use App\Models\WorkScheduleRecord;
use App\Support\WorkScheduleRotationState;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;
use Throwable;

class GenerateEmployeeWorkScheduleRecordsJob implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     */
    public $timeout = 120;

    private Collection $holidays;
    private Collection $leaves;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private readonly WorkSchedule $workSchedule,
        private readonly Employee $employee
    ) {
        $this->onQueue('work-schedule-records');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Load workdays with pivot data and team
        $this->workSchedule->load(['workdays', 'team']);

        // Calculate start date (never in the past)
        $startDate = Carbon::parse($this->workSchedule->start_date)->max(Carbon::today());

        // Generate records for the next 6 months
        $endDate = $startDate->copy()->addMonths(6);

        // Pre-fetch holidays and leaves for the entire period
        $this->preloadHolidaysAndLeaves($startDate, $endDate);

        // Delete existing future records for this employee and work schedule
        WorkScheduleRecord::where('work_schedule_id', $this->workSchedule->id)
            ->where('employee_id', $this->employee->id)
            ->where('date', '>=', $startDate->toDateString())
            ->doesntHave('attendances')
            ->delete();

        // Generate records for all dates in the 6-month period
        $this->generateRecordsForAllDates($startDate, $endDate);
    }

    /**
     * Handle a job failure.
     */
    public function failed(?Throwable $exception): void
    {
        report($exception);
    }

    /**
     * Pre-load holidays and leaves for the entire period to avoid N+1 queries
     */
    private function preloadHolidaysAndLeaves(Carbon $startDate, Carbon $endDate): void
    {
        $this->holidays = Holiday::query()
            ->where('team_id', $this->employee->team_id)
            ->where(function ($query) use ($startDate, $endDate) {
                $query
                    ->whereBetween('start_date', [$startDate, $endDate])
                    ->orWhereBetween('end_date', [$startDate, $endDate])
                    ->orWhere(function ($query) use ($startDate, $endDate) {
                        $query
                            ->where('start_date', '<=', $startDate)
                            ->where('end_date', '>=', $endDate);
                    });
            })
            ->get();

        $this->leaves = $this->employee
            ->leaves()
            ->approved()
            ->where(function ($query) use ($startDate, $endDate) {
                $query
                    ->whereBetween('from_date', [$startDate, $endDate])
                    ->orWhereBetween('to_date', [$startDate, $endDate])
                    ->orWhere(function ($query) use ($startDate, $endDate) {
                        $query
                            ->where('from_date', '<=', $startDate)
                            ->where('to_date', '>=', $endDate);
                    });
            })
            ->get();
    }

    /**
     * Generate records for all dates in the period
     */
    private function generateRecordsForAllDates(Carbon $startDate, Carbon $endDate): void
    {
        if ($this->workSchedule->type === WorkScheduleType::Fixed) {
            $this->generateFixedScheduleRecords($startDate, $endDate);
        } else {
            $this->generateRotationalScheduleRecords($startDate, $endDate);
        }
    }

    /**
     * Generate records for fixed schedule
     */
    private function generateFixedScheduleRecords(Carbon $startDate, Carbon $endDate): void
    {
        $workday = $this->workSchedule->workdays->first();
        if (!$workday) {
            return;
        }

        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            $this->createRecord($currentDate, $workday);
            $currentDate->addDay();
        }
    }

    /**
     * Generate records for rotational schedule with clean, readable logic
     */
    private function generateRotationalScheduleRecords(Carbon $startDate, Carbon $endDate): void
    {
        $workdays = $this->workSchedule->workdays;
        if ($workdays->isEmpty()) {
            return;
        }

        $currentDate = $startDate->copy();
        $rotationState = new WorkScheduleRotationState($workdays);

        while ($currentDate->lte($endDate)) {
            $currentWorkday = $rotationState->getCurrentWorkday();
            $this->createRecord($currentDate, $currentWorkday);

            // Advance the rotation state for the next day
            $rotationState->advanceDay($this->workSchedule);

            $currentDate->addDay();
        }
    }

    private function createRecord(Carbon $date, $workday): void
    {
        $workdayType = $this->determineWorkdayType($date);

        WorkScheduleRecord::create([
            'team_id' => $this->workSchedule->team_id,
            'work_schedule_id' => $this->workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday->id,
            'date' => $date->toDateString(),
            'workday_type' => $workdayType,
        ]);
    }

    private function determineWorkdayType(Carbon $date): WorkdayType
    {
        // Check for holidays first (using preloaded data)
        $isHoliday = $this->holidays->contains(function ($holiday) use ($date) {
            return $date->between($holiday->start_date, $holiday->end_date);
        });

        // Check for approved leave (using preloaded data)
        $onLeave = $this->leaves->contains(function ($leave) use ($date) {
            return $date->between($leave->from_date, $leave->to_date);
        });

        // Check if it's a weekend based on work schedule distribution
        $isWeekend = $this->isWeekendDay($date);

        // Get team vacation weekend policy
        $vacationWeekendPolicy = $this->workSchedule->team->vacation_weekend;

        // Apply the same logic as AttendanceService
        if ($onLeave) {
            return $isWeekend && !$vacationWeekendPolicy
                ? WorkdayType::Weekend
                : WorkdayType::Leave;
        } elseif ($isHoliday) {
            return WorkdayType::Holiday;
        } else {
            return $isWeekend ? WorkdayType::Weekend : WorkdayType::Weekday;
        }
    }

    private function isWeekendDay(Carbon $date): bool
    {
        if (
            $this->workSchedule->work_and_off_days_distribution_type ===
            WorkAndOffDaysDistributionType::SpecificDays
        ) {
            // If using specific days, unselected days are weekends
            $dayName = strtolower($date->format('l'));
            return !in_array($dayName, $this->workSchedule->specific_days ?? []);
        }

        // For number of days distribution, weekend determination is handled by the rotation logic
        // This method is called after rotation logic determines work/off periods
        return false;
    }
}
