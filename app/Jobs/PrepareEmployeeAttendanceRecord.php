<?php

namespace App\Jobs;

use App\Enums\AttendanceStatus;
use App\Enums\CheckoutReminderConfig;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Holiday;
use App\Notifications\CheckoutReminderNotification;
use App\Scopes\TenantScope;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PrepareEmployeeAttendanceRecord implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private readonly CarbonImmutable $date;

    /**
     * @throws Exception date should not be in future
     */
    public function __construct(private readonly Employee $employee, ?CarbonImmutable $date = null)
    {
        if ($date && $date->isFuture()) {
            throw new Exception('date should not be in future');
        }

        $this->date = $date ?: CarbonImmutable::today();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $shift = $this->employee->shift;

        if (!$shift) {
            info('Shift not found for employee, skipping attendance record preparation', [
                'employee_id' => $this->employee->id,
            ]);
            return;
        }

        $shiftFrom = $this->date->setTime(0, 0);
        $shiftTo = $this->date->setTime(0, 0);
        $isWeekend = true;
        $status = Attendance::WEEKEND;
        $isNextDayCheckout = false;
        $isHoliday = Holiday::query()
            ->where('team_id', $this->employee->team_id)
            ->whereDate('start_date', '<=', $this->date)
            ->whereDate('end_date', '>=', $this->date)
            ->exists();

        $vacationWeekendPolicy = $this->employee->team->vacation_weekend;

        $todayShiftConfig = $shift->getDayShift($this->date);

        if ($todayShiftConfig) {
            $shiftFrom = $this->date->setTimeFromTimeString($todayShiftConfig['from']);
            $shiftTo = $this->date->setTimeFromTimeString($todayShiftConfig['to']);
            $isNextDayCheckout = !empty($todayShiftConfig['next_day_checkout']);

            if ($isNextDayCheckout) {
                $shiftTo = $shiftTo->copy()->addDay();
            }

            $isWeekend = false;
            $status = $this->date->isToday() ? Attendance::YET : Attendance::ABSENT;
        }

        $forceCheckout = $shift->force_checkout->setDate(
            $this->date->year,
            $this->date->month,
            $this->date->day
        );

        if ($shiftFrom > $forceCheckout) {
            $forceCheckoutTime = $forceCheckout->copy()->addDay();
        } else {
            $forceCheckoutTime = $forceCheckout;
        }

        if ($isWeekend) {
            $forceCheckoutTime = $this->date->setTime(23, 59);
        }

        // when user for some reason, put force_checkout_time "before" shift_to,
        // for example, shift_to is 18:00 and force_checkout_time is 16:00,
        if ($shiftTo > $forceCheckoutTime) {
            $forceCheckoutTime = $shiftTo;
        }

        $activeUntil = $forceCheckoutTime; // TODO: we have to remove forceCheckoutTime -> even from the database

        if ($isNextDayCheckout) {
            $activeUntil = $todayShiftConfig['prevent_checkout_after']
                ? $this->date
                    ->setTimeFromTimeString($todayShiftConfig['prevent_checkout_after'])
                    ->addDay()
                : $shiftTo;
        }

        $onLeave = $this->employee
            ->leaves()
            ->date($this->date)
            ->approved()
            ->count();

        $data = $this->getStatus(
            status: $status,
            onLeave: $onLeave,
            isWeekend: $isWeekend,
            isHoliday: $isHoliday,
            vacationWeekendPolicy: $vacationWeekendPolicy
        );

        $attendance = Attendance::query()
            ->withoutGlobalScope(TenantScope::class)
            ->firstOrCreate(
                [
                    'employee_id' => $this->employee->id,
                    'date' => $this->date->format('Y-m-d'),
                ],
                [
                    'team_id' => $this->employee->team_id,
                    'shift_id' => $shift->id,
                    'check_in' => null,
                    'check_out' => null,
                    'shift_from' => $shiftFrom,
                    'shift_to' => $shiftTo,
                    'net_hours' => $this->date->setTime(0, 0),
                    'status' => $data['status'],
                    'is_weekend' => $data['is_weekend'],
                    'is_holiday' => $data['is_holiday'],
                    'active_until' => $activeUntil,
                    'force_checkout_time' => $forceCheckoutTime,
                    'flexible_hours' => $shift->working_hours['flexible_hours'],
                    'timezone' => $shift->timezone,
                    'employee_statement_enabled' =>
                        $this->employee->team->employee_statement_config->enabled,
                ]
            );

        if ($data['status'] !== Attendance::YET) {
            return;
        }

        $this->scheduleCheckoutReminder($shiftTo);

        if ($attendance->employee_statement_enabled) {
            SendEmployeeStatementIfApplicableJob::dispatch($attendance)->delay($shiftTo);
        }
    }

    public function getStatus(
        string $status,
        string $onLeave,
        bool $isWeekend,
        bool $isHoliday,
        bool $vacationWeekendPolicy
    ): array {
        if ($onLeave) {
            return [
                'status' =>
                    $isWeekend && !$vacationWeekendPolicy
                        ? AttendanceStatus::WEEKEND
                        : AttendanceStatus::LEAVE,
                'is_weekend' => $isWeekend && !$vacationWeekendPolicy,
                'is_holiday' => false,
            ];
        }

        if ($isHoliday) {
            return [
                'status' => AttendanceStatus::HOLIDAY,
                'is_weekend' => false,
                'is_holiday' => true,
            ];
        }

        return [
            'status' => $isWeekend ? AttendanceStatus::WEEKEND : $status,
            'is_weekend' => $isWeekend,
            'is_holiday' => false,
        ];
    }

    public function scheduleCheckoutReminder(Carbon|CarbonImmutable $shiftTo): void
    {
        if (
            $this->employee->team->checkout_reminder_config === CheckoutReminderConfig::ByShiftEnd
        ) {
            $this->employee->notify(
                (new CheckoutReminderNotification())->delay($shiftTo->subMinutes(15))
            );
        }
    }
}
