<?php

namespace App\Services;

use App\Jobs\GenerateWorkScheduleRecordsJob;
use App\Models\WorkSchedule;
use DB;

class CreateWorkScheduleService
{
    public static function handle(array $data): WorkSchedule
    {
        return DB::transaction(function () use ($data) {
            // Extract workdays and assignments data
            $workdaysData = $data['workdays'];
            $assignmentsData = $data['assignments'] ?? null;
            unset($data['workdays'], $data['assignments']);

            $workSchedule = WorkSchedule::create($data);

            $workDaysPivotToSync = collect($workdaysData)->mapWithKeys(
                fn($workdayData) => [
                    $workdayData['workday_id'] => [
                        'work_days_number' => $workdayData['work_days_number'],
                        'off_days_number' => $workdayData['off_days_number'],
                        'repetitions_number' => $workdayData['repetitions_number'],
                    ],
                ]
            );

            $workSchedule->workdays()->sync($workDaysPivotToSync);

            // Handle assignments
            ManageWorkScheduleAssignmentsService::handle($workSchedule, $assignmentsData);

            // Dispatch job to generate records for assigned employees
            if ($assignmentsData) {
                GenerateWorkScheduleRecordsJob::dispatch($workSchedule);
            }

            return $workSchedule;
        });
    }
}
