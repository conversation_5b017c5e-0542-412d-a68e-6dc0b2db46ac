<?php

namespace App\Rules;

use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleType;
use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;

class WorkScheduleRepetitionsRule implements ValidationRule, DataAwareRule
{
    protected array $data = [];

    public function setData(array $data): static
    {
        $this->data = $data;
        return $this;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $type = $this->data['type'] ?? null;
        $distributionType = $this->data['work_and_off_days_distribution_type'] ?? null;

        // Required ONLY for rotational schedules with number of days distribution
        if ($type === WorkScheduleType::Rotational->value &&
            $distributionType === WorkAndOffDaysDistributionType::NumberOfDays->value &&
            ($value === null || $value === '')) {
            $fail('Repetitions number is required for rotational schedules with number of days distribution.');
            return;
        }

        // Prohibited for fixed schedules
        if ($type === WorkScheduleType::Fixed->value && $value !== null && $value !== '') {
            $fail('Repetitions number cannot be used with fixed schedules.');
            return;
        }

        // Prohibited for specific days distribution (regardless of schedule type)
        if ($distributionType === WorkAndOffDaysDistributionType::SpecificDays->value && $value !== null && $value !== '') {
            $fail('Repetitions number cannot be used with specific days distribution.');
            return;
        }
    }
}
