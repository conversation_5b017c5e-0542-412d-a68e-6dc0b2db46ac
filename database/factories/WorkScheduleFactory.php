<?php

namespace Database\Factories;

use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleType;
use App\Models\Team;
use App\Models\WorkSchedule;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WorkSchedule>
 */
class WorkScheduleFactory extends Factory
{
    protected $model = WorkSchedule::class;

    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'type' => fake()->randomElement(WorkScheduleType::cases()),
            'name' => fake()->unique()->words(2, true) . ' Schedule',
            'work_and_off_days_distribution_type' => fake()->randomElement(
                WorkAndOffDaysDistributionType::cases()
            ),
            'specific_days' => null,
            'off_days_after_each_repetition' => fake()->numberBetween(0, 5),
            'start_date' => fake()->dateTimeBetween('now', '+1 month'),
        ];
    }

    public function fixed(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'type' => WorkScheduleType::Fixed,
            ]
        );
    }

    public function rotational(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'type' => WorkScheduleType::Rotational,
            ]
        );
    }

    public function numberOfDays(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'work_and_off_days_distribution_type' =>
                    WorkAndOffDaysDistributionType::NumberOfDays,
                'specific_days' => null,
                'off_days_after_each_repetition' => fake()->numberBetween(0, 5),
            ]
        );
    }

    public function specificDays(): static
    {
        $days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        $selectedDays = fake()->randomElements($days, fake()->numberBetween(1, 5));

        return $this->state(
            fn(array $attributes) => [
                'work_and_off_days_distribution_type' =>
                    WorkAndOffDaysDistributionType::SpecificDays,
                'specific_days' => $selectedDays,
                'off_days_after_each_repetition' => 0,
            ]
        );
    }
}
