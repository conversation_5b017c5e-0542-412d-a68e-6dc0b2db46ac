<?php

namespace Database\Factories;

use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleType;
use App\Models\Team;
use App\Models\WorkSchedule;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WorkSchedule>
 */
class WorkScheduleFactory extends Factory
{
    protected $model = WorkSchedule::class;

    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'type' => fake()->randomElement(WorkScheduleType::cases()),
            'name' => fake()->unique()->words(2, true) . ' Schedule',
            'work_and_off_days_distribution_type' => fake()->randomElement(
                WorkAndOffDaysDistributionType::cases()
            ),
            'off_days_after_each_repetition' => fake()->numberBetween(0, 5),
            'start_date' => fake()->dateTimeBetween('now', '+1 month'),
        ];
    }

    public function fixed(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'type' => WorkScheduleType::Fixed,
            ]
        );
    }

    public function rotational(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'type' => WorkScheduleType::Rotational,
            ]
        );
    }

    public function numberOfDays(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'work_and_off_days_distribution_type' =>
                    WorkAndOffDaysDistributionType::NumberOfDays,
                'off_days_after_each_repetition' => fake()->numberBetween(0, 5),
            ]
        );
    }

    public function specificDays(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'work_and_off_days_distribution_type' =>
                    WorkAndOffDaysDistributionType::SpecificDays,
                'off_days_after_each_repetition' => 0,
            ]
        );
    }
}
