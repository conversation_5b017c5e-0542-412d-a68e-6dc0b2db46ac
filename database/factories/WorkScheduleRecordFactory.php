<?php

namespace Database\Factories;

use App\Enums\WorkdayType;
use App\Models\Employee;
use App\Models\Team;
use App\Models\Workday;
use App\Models\WorkSchedule;
use App\Models\WorkScheduleRecord;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WorkScheduleRecord>
 */
class WorkScheduleRecordFactory extends Factory
{
    protected $model = WorkScheduleRecord::class;

    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'work_schedule_id' => WorkSchedule::factory(),
            'employee_id' => Employee::factory(),
            'workday_id' => Workday::factory(),
            'date' => fake()->dateTimeBetween('now', '+6 months'),
            'workday_type' => fake()->randomElement(WorkdayType::cases()),
        ];
    }

    public function weekday(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'workday_type' => WorkdayType::Weekday,
            ]
        );
    }

    public function weekend(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'workday_type' => WorkdayType::Weekend,
            ]
        );
    }

    public function holiday(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'workday_type' => WorkdayType::Holiday,
            ]
        );
    }
}
